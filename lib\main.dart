import 'dart:async';

import 'package:clarity_flutter/clarity_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/repository/auth_token_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_environment.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/core_main/resources/theme/app_theme.dart';
import 'package:leadrat/core_main/services/call_detection_status_service/call_detection_status_service.dart';
import 'package:leadrat/core_main/utilities/app_life_cycle_observer.dart';
import 'package:leadrat/core_main/utilities/app_utils.dart';
import 'package:leadrat/core_main/utilities/native_call_tracker.dart';
import 'package:leadrat/features/home/<USER>/pages/splash_screen.dart';

import 'firebase_options.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

Future<void> initAppDependency() async {
  RestResources.setAppEnvironment(AppEnvironment.qa);
  await initDependencies();
  await getIt<AuthTokenRepository>().getBaseUrl();
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initAppDependency();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  final config = ClarityConfig(projectId: StringConstants.microsoftClarityProjectId, logLevel: LogLevel.None);
  await NativeCallTracker.initialize();

  if (kReleaseMode) {
    runApp(ClarityWidget(app: const MyApp(), clarityConfig: config));
  } else {
    runApp(const MyApp());
  }
}

class MyApp extends StatefulWidget {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late CallLifecycleObserver _callLifecycleObserver;
  CallDetectionStatusService? _callDetectionStatusService;

  @override
  void initState() {
    super.initState();
    _callLifecycleObserver = CallLifecycleObserver();
    WidgetsBinding.instance.addObserver(_callLifecycleObserver);

    // Delay call detection service initialization to prevent interference with app startup
    // Reduced delay since restart issue is now fixed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 2), () {
        try {
          // Check if widget is still mounted before initializing
          if (mounted && _callDetectionStatusService == null) {
            _callDetectionStatusService = getIt<CallDetectionStatusService>();
            _callDetectionStatusService?.initialize();
            debugPrint('CallDetectionStatusService initialized after app stabilization');
          }
        } catch (e) {
          debugPrint('Error initializing CallDetectionStatusService: $e');
        }
      });
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(_callLifecycleObserver);
    try {
      _callDetectionStatusService?.dispose();
    } catch (e) {
      debugPrint('Error disposing CallDetectionStatusService: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final whiteLabeledAppName = AppUtils.getAppName();
    return MultiBlocProvider(
      providers: registerBlocProviders(context),
      child: MaterialApp(
        title: whiteLabeledAppName,
        navigatorKey: MyApp.navigatorKey,
        navigatorObservers: [routeObserver],
        debugShowCheckedModeBanner: false,
        theme: AppTheme.darkThemeMode,
        home: const SplashScreen(),
      ),
    );
  }
}
