import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/lead_source_enum.dart';
import 'package:leadrat/core_main/enums/common/marital_status_type.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/buyer_type.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/enums/user_profile/gender_enum.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/enum_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/date_time_utils.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/core_main/utilities/phone_utils.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/data/models/add_lead_model.dart';
import 'package:leadrat/features/lead/data/models/create_enquiry_model.dart';
import 'package:leadrat/features/lead/data/models/lead_project_unit_model.dart';
import 'package:leadrat/core_main/common/models/selected_project_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_sub_source_entity.dart';
import 'package:leadrat/features/lead/domain/repository/leads_repository.dart';
import 'package:leadrat/features/lead/domain/usecase/add_lead_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/check_lead_assigned_by_leadId_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/get_channel_partner_names_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_by_contact_no_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_sub_source_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_property_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/lead_project_unit_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_use_case.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/manage_leads_bloc/manage_leads_bloc.dart';
import 'package:leadrat/main.dart';

part 'add_lead_event.dart';

part 'add_lead_state.dart';

class AddLeadBloc extends Bloc<AddLeadEvent, AddLeadState> {
  //TextEditing Controller
  TextEditingController? leadNameController;
  TextEditingController? phoneController;
  TextEditingController? altPhoneController;
  TextEditingController? emailController;
  TextEditingController? minBudgetController;
  TextEditingController? maxBudgetController;
  TextEditingController? companyNameController;
  TextEditingController? carpetAreaController;
  TextEditingController? notesController;
  TextEditingController? referralNameController;
  TextEditingController? referralPhoneController;
  TextEditingController? designationController;
  TextEditingController? saleableAreaAreaController;
  TextEditingController? builtUpAreaController;
  TextEditingController? landlineNumberController;

  TextEditingController? referralEmailController;
  TextEditingController? executiveNameController;
  TextEditingController? executivePhoneController;
  TextEditingController? paymentPlan1Controller;
  TextEditingController? paymentPlan2Controller;

  final _genders = GenderEnum.values.where((element) => element != GenderEnum.notMentioned).map((e) => SelectableItem<GenderEnum>(title: e == GenderEnum.other ? "other" : e.description, value: e)).toList();
  final _maritalStatus = MaritalStatusType.values.where((element) => element != MaritalStatusType.notMentioned).map((e) => SelectableItem<MaritalStatusType>(title: e.description, value: e)).toList();

  //private fields
  List<MasterPropertyTypeModel>? _masterPropertyTypes;
  AddLeadModel _addLeadModel = AddLeadModel(enquiry: CreateLeadEnquiryModel());
  List<LeadSubSourceEntity> _allLeadSource = [];
  GetLeadEntity? getLeadEntity;
  bool isEditing = false;
  List<GetAllUsersModel?>? _allUsers = [];
  GlobalSettingModel? globalSettings;

  //DI
  final MasterDataRepository _masterDataRepository;
  final UsersDataRepository _usersDataRepository;
  final LeadsRepository _leadsRepository;
  final AddLeadUseCase _addLeadUseCase;
  final UpdateLeadUseCase _updateLeadUseCase;
  final GetPropertyNameWithIdUseCase _getPropertyNameWithIdUseCase;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetLeadSubSourceUseCase _getLeadSubSourceUseCase;
  final GetLeadByContactNoUseCase _getLeadByContactNoUseCase;
  final GlobalSettingRepository _globalSettingRepository;
  final GetChannelPartnerNamesUseCase _getChannelPartnerNamesUseCase;
  final AppAnalysisRepository _appAnalysisRepository;
  final CheckLeadAssignedByLeadIdUseCase _checkLeadAssignedByLeadIdUseCase;
  final LeadProjectUnitUseCase leadProjectUnitUseCase;

  AddLeadBloc(
    this._masterDataRepository,
    this._usersDataRepository,
    this._leadsRepository,
    this._addLeadUseCase,
    this._getLeadSubSourceUseCase,
    this._getProjectNameWithIdUseCase,
    this._getPropertyNameWithIdUseCase,
    this._updateLeadUseCase,
    this._getLeadByContactNoUseCase,
    this._globalSettingRepository,
    this._getChannelPartnerNamesUseCase,
    this._appAnalysisRepository,
    this._checkLeadAssignedByLeadIdUseCase,
    this.leadProjectUnitUseCase,
  ) : super(const AddLeadState()) {
    on<AddLeadInitialEvent>(_onAddLeadInitial);
    on<ToggleEnquiredForEvent>(_onToggleEnquiredFor);
    on<TogglePropertyTypeEvent>(_onTogglePropertyType);
    on<TogglePropertySubTypeEvent>(_onTogglePropertySubType);
    on<ToggleSubTypesExpandedEvent>(_onToggleSubTypesExpanded);
    on<ToggleProfessionEvent>(_onToggleProfession);
    on<ToggleNoOfBhkExpandedEvent>(_onToggleNoOfBhkExpanded);
    on<ToggleNoOfBhkEvent>(_onToggleNoOfBhk);
    on<ToggleBhkTypeEvent>(_onToggleBhkType);
    on<CreateLeadEvent>(_onCreateLead);
    on<SelectLeadSourceEvent>(_onSelectLeadSource);
    on<SelectLeadSubSourceEvent>(_onSelectLeadSubSource);
    on<SelectAgencyNameEvent>(_onSelectAgencyName);
    on<SelectCampaignNameEvent>(_onSelectCampaignName);
    on<SelectCarpetAreaEvent>(_onSelectCarpetArea);
    on<SelectProjectsEvent>(_onSelectProjects);
    on<SelectPropertiesEvent>(_onSelectProperties);
    on<RemoveProjectsEvent>(_onRemoveProjects);
    on<RemovePropertyEvent>(_onRemoveProperty);
    on<RemoveAgencyNameEvent>(_onRemoveAgencyName);
    on<RemoveCampaignNameEvent>(_onRemoveCampaignName);
    on<ResetStateEvent>(_onResetState);
    on<ToggleReferralFieldsEvent>(_onToggleReferralFields);
    on<ToggleAltPhoneFieldEvent>(_onToggleAltPhoneField);
    on<ToggleEmailFieldEvent>(_onToggleEmailField);
    on<TogglePossessionDateEvent>(_onTogglePossessionDate);
    on<SelectPossessionDateEvent>(_onSelectPossessionDate);
    on<SelectAssignedUserEvent>(_onSelectAssignedUser);
    on<SelectSecondaryUserEvent>(_onSelectSecondaryUser);
    on<AddLocationEvent>(_onAddLocation);
    on<RemoveLocationEvent>(_onRemoveLocation);
    on<CheckLeadContactAlreadyExistsEvent>(_onCheckLeadContactAlreadyExists);
    on<CheckAltContactAlreadyExistsEvent>(_onCheckAltContactAlreadyExists);
    on<OnLeadContactChangedEvent>(_onOnLeadContactChanged);
    on<OnAltContactChangedEvent>(_onOnAltContactChanged);
    on<OnReferralContactChangedEvent>(_onOnReferralContactChanged);
    on<PickContactEvent>(_onPickContact);
    on<SelectCurrency>(_onCurrencySelect);
    on<SelectChannelPartnerEvent>(_onSelectedChannelPartners);
    on<RemoveChannelPartnerNameEvent>(_onRemoveChannelPartnerName);
    on<SelectSaleableAreaEvent>(_onSelectSaleableAreaEvent);
    on<SelectBuiltUpAreaEvent>(_onSelectBuiltUpAreaEvent);
    on<SelectPurposeEvent>(_onSelectPurpose);
    on<AssignedToLoggedInUser>(_onAssignedToLoggedInUser);
    on<SelectPossessionType>(_onSelectPossessionType);
    on<SelectSourcingManagerEvent>(_onSelectSourcingManager);
    on<SelectClosingManagerEvent>(_onSelectClosingManager);
    on<AddCustomerLocationEvent>(_onAddCustomerLocation);
    on<RemoveCustomerLocationEvent>(_onRemoveCustomerLocation);
    on<OnExecutiveContactChangedEvent>(_onExecutiveContactChanged);
    on<SelectGenderEvent>(_onSelectGender);
    on<SelectDateOfBirthEvent>(_onSelectDateOfBirth);
    on<SelectMaritalStatusEvent>(_onSelectMaritalStatus);
    on<DuplicateSourceCheck>(_onDuplicateSourceCheck);
    on<SelectAnniversaryDateEvent>(_onSelectAnniversaryDate);
    on<SelectBuyerEvent>(_onSelectBuyer);
    on<SelectProjectUnitEvent>(_onSelectProjectUnitEvent);
    on<RemoveProjectsUnitEvent>(_onRemoveProjectsUnitEvent);
  }

  void initTextController() {
    if (_isDisposed) return; // Don't initialize if already disposed

    leadNameController = TextEditingController();
    phoneController = TextEditingController();
    altPhoneController = TextEditingController();
    emailController = TextEditingController();
    minBudgetController = TextEditingController();
    maxBudgetController = TextEditingController();
    companyNameController = TextEditingController();
    carpetAreaController = TextEditingController();
    notesController = TextEditingController();
    referralNameController = TextEditingController();
    referralPhoneController = TextEditingController();
    designationController = TextEditingController();
    saleableAreaAreaController = TextEditingController();
    builtUpAreaController = TextEditingController();
    landlineNumberController = TextEditingController();
    referralEmailController = TextEditingController();
    executivePhoneController = TextEditingController();
    executiveNameController = TextEditingController();
    paymentPlan2Controller = TextEditingController();
    paymentPlan1Controller = TextEditingController();
  }

  bool _isDisposed = false;

  void disposeTextController() {
    if (_isDisposed) return; // Prevent double disposal

    _isDisposed = true;

    leadNameController?.dispose();
    leadNameController = null;
    phoneController?.dispose();
    phoneController = null;
    altPhoneController?.dispose();
    altPhoneController = null;
    emailController?.dispose();
    emailController = null;
    minBudgetController?.dispose();
    minBudgetController = null;
    maxBudgetController?.dispose();
    maxBudgetController = null;
    companyNameController?.dispose();
    companyNameController = null;
    carpetAreaController?.dispose();
    carpetAreaController = null;
    notesController?.dispose();
    notesController = null;
    referralNameController?.dispose();
    referralNameController = null;
    referralPhoneController?.dispose();
    referralPhoneController = null;
    designationController?.dispose();
    designationController = null;
    saleableAreaAreaController?.dispose();
    saleableAreaAreaController = null;
    builtUpAreaController?.dispose();
    builtUpAreaController = null;
    landlineNumberController?.dispose();
    landlineNumberController = null;
    referralEmailController?.dispose();
    referralEmailController = null;
    executivePhoneController?.dispose();
    executivePhoneController = null;
    executiveNameController?.dispose();
    executiveNameController = null;
    paymentPlan2Controller?.dispose();
    paymentPlan2Controller = null;
    paymentPlan1Controller?.dispose();
    paymentPlan1Controller = null;
  }

  FutureOr<void> _onAddLeadInitial(AddLeadInitialEvent event, Emitter<AddLeadState> emit) async {
    getLeadEntity = event.getLeadEntity;
    isEditing = getLeadEntity != null && (getLeadEntity?.id.isNotNullOrEmpty() ?? false);
    _addLeadModel = AddLeadModel(enquiry: CreateLeadEnquiryModel(), scheduledDate: getLeadEntity?.scheduledDate, assignedFrom: getLeadEntity?.assignedFromUser?.id);
    globalSettings = await _globalSettingRepository.getGlobalSettings();
    final defaultDialCode = globalSettings?.countries?.firstOrNull?.defaultCallingCode;
    emit(state.copyWith(showDialogProgress: isEditing, globalSettingModel: globalSettings, defaultCountryCode: defaultDialCode));
    _allUsers = await _usersDataRepository.getAssignUser();
    await _initRemoteData(emit);
    _initEnquiredFor(emit);
    _initPropertyTypes(emit);
    _initProfessions(emit);
    _initPurpose(emit);
    _initPossessionType(emit);
    if (isEditing) await _initInitialData(emit);
    List<SelectableItem<BuyerType>>? buyersList = BuyerType.values.where((element) => element != BuyerType.none).map((e) => SelectableItem<BuyerType>(title: e.description, value: e)).toList();

    emit(state.copyWith(
      buyersList: buyersList,
      pageState: PageState.initial,
      globalSettingModel: globalSettings,
      isAltPhoneFieldVisible: isEditing ? altPhoneController?.text.isNotEmpty : state.isAltPhoneFieldVisible,
      isEmailFieldVisible: isEditing ? emailController?.text.isNotEmpty : state.isEmailFieldVisible,
      isReferralDetailsVisible: (referralPhoneController?.text.isNotEmpty ?? false) || (referralNameController?.text.isNotEmpty ?? false) || (referralEmailController?.text.isNotEmpty ?? false),
      isPossessionDateVisible: getLeadEntity?.enquiry?.possessionDate != null,
      possessionDate: getLeadEntity?.enquiry?.possessionDate?.toUserTimeZone(),
      contactNumber: getLeadEntity?.contactNo,
      genders: _genders,
      maritalStatusTypes: _maritalStatus,
    ));
    emit(state.copyWith(showDialogProgress: false));
  }

  void _initEnquiredFor(Emitter<AddLeadState> emit) {
    final selectedEnquiredTypes = getLeadEntity?.enquiry?.enquiryTypes?.nonNulls;
    final enquiryTypes = EnquiryType.values.where((type) => type != EnquiryType.none).map((type) => ItemSimpleModel<EnquiryType>(title: type.description, value: type)).toList();
    emit(state.copyWith(enquiredFor: enquiryTypes));
    if (selectedEnquiredTypes != null) {
      for (var element in selectedEnquiredTypes) {
        final selectedEnquiry = enquiryTypes.firstWhereOrNull((enquiry) => enquiry.value == element);
        add(ToggleEnquiredForEvent(selectedEnquiry));
      }
    }
  }

  void _initPropertyTypes(Emitter<AddLeadState> emit) {
    final initSelectedPropertyType = getLeadEntity?.enquiry?.propertyType;
    final propertyTypes = PropertyType.values.map((type) => ItemSimpleModel<PropertyType>(title: type.description, value: type, description: type.baseId)).toList();
    emit(state.copyWith(propertyTypes: propertyTypes));
    if (initSelectedPropertyType != null) {
      final selectedPropertyType = propertyTypes.firstWhereOrNull((element) => element.description == initSelectedPropertyType.id);
      if (selectedPropertyType != null) add(TogglePropertyTypeEvent(selectedPropertyType));
    }
  }

  void _initProfessions(Emitter<AddLeadState> emit) {
    final initSelectedProfession = getLeadEntity?.profession;
    final professions = Profession.values.where((profession) => profession != Profession.none).map((profession) => ItemSimpleModel<Profession>(title: profession.description, value: profession)).toList();
    emit(state.copyWith(professions: professions));
    if (initSelectedProfession != null) add(ToggleProfessionEvent(professions.firstWhereOrNull((element) => element.value == initSelectedProfession)));
  }

  Future<void> _initRemoteData(Emitter<AddLeadState> emit) async {
    try {
      _masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: getIt<LeadratHomeBloc>().isPropertyListingEnabled);
      await Future.wait([
        _initMasterAreaUnits(emit),
        _initMasterLeadSource(emit),
        _initAgencyNames(emit),
        _initCampaignNames(emit),
        _initAssignToUsers(emit),
        _initProperties(emit),
        _initProjects(emit),
        _initCurrencies(emit),
        _initChannelPartnerNames(emit),
        _initClosingManager(emit),
        _initSourcingManager(emit),
      ]);
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
    }
  }

  Future<void> _initChannelPartnerNames(Emitter<AddLeadState> emit) async {
    try {
      final initSelectedChannelPartner = getLeadEntity?.channelPartners;
      final channelPartnersResponse = await _getChannelPartnerNamesUseCase(NoParams());
      channelPartnersResponse.fold(
        (failure) => null,
        (success) {
          if (success != null && success.isNotEmpty) {
            final selectedAgencyNames = initSelectedChannelPartner?.nonNulls.map((e) => e.firmName).toList();
            final channelPartners = success.map((name) => SelectableItem<String>(title: name ?? '', isSelected: selectedAgencyNames?.contains(name) ?? false, value: name)).toList();
            final selectedChannelPartners = channelPartners.where((element) => element.isSelected).toList();
            emit(state.copyWith(channelPartners: channelPartners, selectedChannelPartners: selectedChannelPartners));
            final selectedChannelPartnerNames = selectedChannelPartners.map((e) => e.title).toList();
            _addLeadModel = _addLeadModel.copyWith(channelPartnerList: selectedChannelPartnerNames);
          }
        },
      );
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  Future<void> _initCurrencies(Emitter<AddLeadState> emit) async {
    try {
      final countries = globalSettings?.countries;
      List<SelectableItem<String>> allCurrencies = [];
      SelectableItem<String>? selectedCurrency;
      countries?.firstOrNull?.currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item.currency ?? '', value: item.currency)));
      if (countries != null) {
        var defaultSymbol = getLeadEntity?.enquiry?.currency ?? globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "INR";
        selectedCurrency = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
        _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(currency: selectedCurrency?.value ?? 'INR'));
      }
      emit(state.copyWith(currencies: allCurrencies, selectedCurrency: selectedCurrency));
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  Future<void> _initMasterAreaUnits(Emitter<AddLeadState> emit) async {
    final initSelectedCarpetArea = (getLeadEntity?.enquiry?.carpetAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.carpetAreaUnitId;
    final initSelectedSaleableArea = (getLeadEntity?.enquiry?.saleableAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.saleableAreaUnitId;
    final initSelectedBuiltUpArea = (getLeadEntity?.enquiry?.builtUpAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.builtUpAreaUnitId;
    final initSelectedNetArea = (getLeadEntity?.enquiry?.netAreaUnitId?.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.netAreaUnitId;
    final initSelectedPropertyArea = (getLeadEntity?.enquiry?.propertyAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.propertyAreaUnitId;
    var masterAreaUnits = await _masterDataRepository.getAreaUnits();
    if (masterAreaUnits?.isNotEmpty ?? false) {
      final carpetAreaUnits = masterAreaUnits!.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedCarpetArea)).toList();
      final saleableAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedSaleableArea)).toList();
      final builtUpAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedBuiltUpArea)).toList();
      final netAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedNetArea)).toList();
      final propertyAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedPropertyArea)).toList();
      emit(state.copyWith(carpetAreas: carpetAreaUnits, saleableAreas: saleableAreaUnits, builtUpAreas: builtUpAreaUnits, errorMessage: ''));
      final selectedCarpetAea = carpetAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedSaleableArea = saleableAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedBuiltUpArea = builtUpAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedNetArea = netAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedPropertyArea = propertyAreaUnits.firstWhereOrNull((element) => element.isSelected);
      if (selectedCarpetAea != null) {
        add(SelectCarpetAreaEvent(selectedCarpetAea));
      }
      if (selectedSaleableArea != null) {
        add(SelectSaleableAreaEvent(selectedSaleableArea));
      }
      if (selectedBuiltUpArea != null) {
        add(SelectBuiltUpAreaEvent(selectedBuiltUpArea));
      }
      if (selectedNetArea != null) {
        add(SelectCarpetAreaEvent(selectedBuiltUpArea));
      }
      if (selectedPropertyArea != null) {
        add(SelectCarpetAreaEvent(selectedBuiltUpArea));
      }
    }
  }

  Future<void> _initMasterLeadSource(Emitter<AddLeadState> emit) async {
    final initialSelectedLeadSource = getLeadEntity?.enquiry?.leadSource ?? LeadSource.direct;
    final leadSource = await _getLeadSubSourceUseCase(NoParams());
    leadSource.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allLeadSource = success
            .where((element) => element.leadSourceEntity?.isEnabled ?? false)
            .map((e) => SelectableItem<int>(
                  title: e.leadSourceEntity?.displayName ?? "",
                  value: e.leadSourceEntity?.value,
                  isSelected: (e.leadSourceEntity?.value != null) ? initialSelectedLeadSource.value == e.leadSourceEntity?.value : false,
                ))
            .toList();
        _allLeadSource = success;
        emit(state.copyWith(
          leadSource: allLeadSource,
          errorMessage: '',
        ));
        final selectedLeadSource = allLeadSource.firstWhereOrNull((element) => element.isSelected);
        if (selectedLeadSource != null) add(SelectLeadSourceEvent(selectedLeadSource: selectedLeadSource));
      }
    });
  }

  Future<void> _initAgencyNames(Emitter<AddLeadState> emit) async {
    final initialSelectedAgencyName = getLeadEntity?.agencies?.map((e) => e.name).nonNulls;
    var agencyNames = await _masterDataRepository.getAgencyNames();
    if (agencyNames?.isNotEmpty ?? false) {
      final agenciesNames = agencyNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedAgencyName?.contains(name) ?? false)).toList();
      final selectedAgencyNames = agenciesNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(agencyNames: agenciesNames, errorMessage: '', selectedAgencyNames: selectedAgencyNames));
      _addLeadModel = _addLeadModel.copyWith(agencies: selectedAgencyNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initCampaignNames(Emitter<AddLeadState> emit) async {
    final initialSelectedCampaignNames = getLeadEntity?.campaigns?.map((e) => e.name).nonNulls;
    var campaignNames = await _leadsRepository.getCampaignNames();
    if (campaignNames?.isNotEmpty ?? false) {
      final campaignsNames = campaignNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedCampaignNames?.contains(name) ?? false)).toList();
      final selectedCamapaignNames = campaignsNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(campaignNames: campaignsNames, errorMessage: '', selectedCampaignNames: selectedCamapaignNames));
      _addLeadModel = _addLeadModel.copyWith(campaigns: selectedCamapaignNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initAssignToUsers(Emitter<AddLeadState> emit) async {
    final initSelectedPrimaryUserId = getLeadEntity?.assignedUser?.id;
    final initSelectedSecondaryUserId = getLeadEntity?.secondaryUser?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var assignToUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSelectedPrimaryUserId == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => assignToUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSelectedPrimaryUserId == disabledUsers?.id, isEnabled: false)));
      final assignSecondaryUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSelectedSecondaryUserId == user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => assignSecondaryUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSelectedPrimaryUserId == disabledUsers?.id, isEnabled: false)));
      assignToUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSelectedPrimaryUserId));
      assignSecondaryUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSelectedSecondaryUserId));
      emit(state.copyWith(assignToUsers: assignToUsers, secondaryUsers: assignSecondaryUsers, errorMessage: ''));
      final selectedPrimaryUser = assignToUsers.firstWhereOrNull((element) => element.isSelected);
      final selectedSecondaryUser = assignSecondaryUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedPrimaryUser != null) add(SelectAssignedUserEvent(selectedPrimaryUser));
      if (selectedSecondaryUser != null) add(SelectSecondaryUserEvent(selectedSecondaryUser));
    }
  }

  Future<void> _initProperties(Emitter<AddLeadState> emit) async {
    final initialSelectedPropertiesId = getLeadEntity?.properties?.map((e) => e.id).nonNulls.toSet() ?? {};
    final properties = await _getPropertyNameWithIdUseCase(NoParams());
    properties.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProperties = success.map((e) => SelectableItem<String>(title: e.title ?? "", value: e.id, isSelected: initialSelectedPropertiesId.contains(e.id))).toList();
        final selectedProperties = allProperties.where((element) => element.isSelected).toList();
        emit(state.copyWith(properties: allProperties, errorMessage: '', selectedProperties: selectedProperties));
        _addLeadModel = _addLeadModel.copyWith(propertiesList: selectedProperties.map((e) => e.title).toList());
      }
    });
  }

  Future<void> _initProjects(Emitter<AddLeadState> emit) async {
    final initialSelectedProjectsId = getLeadEntity?.projects?.map((e) => e.id).nonNulls;
    final projects = await _getProjectNameWithIdUseCase(NoParams());
    projects.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProjects = success.map((e) => SelectableItem<String>(title: e.name ?? "", value: e.id, isSelected: initialSelectedProjectsId?.contains(e.id) ?? false)).toList();
        final selectedProjects = allProjects.where((element) => element.isSelected).toList();
        emit(state.copyWith(projects: allProjects, errorMessage: '', selectedProjects: selectedProjects));
        _addLeadModel = _addLeadModel.copyWith(projectsList: selectedProjects.map((e) => e.title).toList());
      }
    });
    await _initProjectUnits(emit);
  }

  Future<void> _initProjectUnits(Emitter<AddLeadState> emit) async {
    List<SelectableItem<LeadProjectUnitModel>>? selectableProjectUnit = [];
    final initialSelectedProjectUnit = getLeadEntity?.unitTypes?.map((e) => e.id).toList();
    final projectUnits = await leadProjectUnitUseCase(SelectedProjectModel(selectedProjects: state.selectedProjects?.map((e) => e.title).toList()));
    projectUnits.fold(
      (l) => null,
      (success) {
        if (success != null && success.isNotEmpty) {
          success.values.toList().forEach((element) {
            element.toList().forEach((action) {
              selectableProjectUnit.add(
                SelectableItem(title: action.unitName ?? '', value: action),
              );
            });
          });

          final allProjectsUnit = selectableProjectUnit.map((e) => SelectableItem<LeadProjectUnitModel>(title: e.title ?? "", value: e.value, isSelected: initialSelectedProjectUnit?.contains(e.value?.unitId) ?? false)).toList();
          final selectedProjectsUnit = allProjectsUnit.where((element) => element.isSelected).toList();
          emit(state.copyWith(selectableProjectUnit: allProjectsUnit, errorMessage: '', selectedProjectsUnits: selectedProjectsUnit));
          _addLeadModel = _addLeadModel.copyWith(unitIds: selectedProjectsUnit.map((e) => e.value?.unitId ?? '').toList());
        }
      },
    );
  }

  FutureOr<void> _onToggleEnquiredFor(ToggleEnquiredForEvent event, Emitter<AddLeadState> emit) async {
    if (event.selectedEnquiredFor == null) return;
    final updatedEnquiredFor = state.enquiredFor.map((e) => e.value == event.selectedEnquiredFor?.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(enquiredFor: updatedEnquiredFor, errorMessage: ''));
    final selectedEnquiryTypes = state.enquiredFor.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(enquiryTypes: selectedEnquiryTypes));
  }

  FutureOr<void> _onTogglePropertyType(TogglePropertyTypeEvent event, Emitter<AddLeadState> emit) async {
    final updatedPropertyTypes = state.propertyTypes.map((e) => e.description == event.selectedPropertyType.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    if (event.selectedPropertyType.isSelected) {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(propertyTypeId: StringConstants.emptyGuidId, propertyTypeIds: [], bhks: [], bhkTypes: []));
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: [], errorMessage: '', bhkTypes: [], noOfBhk: []));
    } else {
      final propertySubTypes = initPropertySubTypes(event.selectedPropertyType.description ?? '', emit);
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: propertySubTypes, errorMessage: ''));
      _initBhkAndBhkTypes(emit);
    }
  }

  List<ItemSimpleModel<String>> initPropertySubTypes(String propertyTypeId, [Emitter<AddLeadState>? emit]) {
    if (_masterPropertyTypes == null) return [];
    final initSelectedPropertySubTypesId = getLeadEntity?.enquiry?.propertyTypes?.map((p) => p.childType?.id).toList();
    final propertySubTypes = _masterPropertyTypes!
        .where((type) => type.id == propertyTypeId && type.childTypes != null)
        .expand((type) => type.childTypes!)
        .map(
          (subType) => ItemSimpleModel<String>(title: subType.displayName ?? "", value: subType.id, description: subType.baseId),
        )
        .toList();
    if (initSelectedPropertySubTypesId?.isNotEmpty ?? false) {
      final bool shouldExpand = propertySubTypes.any((element) {
        final isSelected = initSelectedPropertySubTypesId!.contains(element.value);
        final index = propertySubTypes.indexOf(element);
        return isSelected && index > 3;
      });

      if (emit != null && shouldExpand) {
        emit(state.copyWith(isSubTypesExpanded: true));
      }

      final selectedPropertySubTypes = propertySubTypes.where((element) => initSelectedPropertySubTypesId?.contains(element.value) ?? false).toList();
      for (var selectedPropertySubType in selectedPropertySubTypes) {
        add(TogglePropertySubTypeEvent(selectedPropertySubType));
      }
    }
    return propertySubTypes;
  }

  FutureOr<void> _onSelectLeadSource(SelectLeadSourceEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(selectedLeadSource: event.selectedLeadSource, errorMessage: ''));
    _initLeadSubSource(event.selectedLeadSource.value, emit);

    if (isEditing && getLeadEntity?.enquiry?.leadSource?.value != event.selectedLeadSource.value && (getLeadEntity?.contactNo?.isNotNullOrEmpty() ?? false)) {
      final contactNumber = PhoneUtils.processPhoneNumber(state.contactNumber ?? getLeadEntity?.contactNo ?? '');
      if (contactNumber.$1.isNotNullOrEmpty() && contactNumber.$2.isNotNullOrEmpty()) {
        add(CheckLeadContactAlreadyExistsEvent(countryCode: contactNumber.$1 ?? '', contactNo: contactNumber.$2 ?? ''));
      }
    } else if (isEditing && getLeadEntity?.enquiry?.leadSource?.value != event.selectedLeadSource.value && (getLeadEntity?.alternateContactNo?.isNotNullOrEmpty() ?? false)) {
      final contactNumber = PhoneUtils.processPhoneNumber(state.altContactNumber ?? getLeadEntity?.alternateContactNo ?? '');
      if (contactNumber.$1.isNotNullOrEmpty() && contactNumber.$2.isNotNullOrEmpty()) {
        add(CheckAltContactAlreadyExistsEvent(countryCode: contactNumber.$1 ?? '', contactNo: contactNumber.$2 ?? ''));
      }
    } else {
      add(DuplicateSourceCheck());
    }
    final selectedSource = event.selectedLeadSource.isSelected ? event.selectedLeadSource.value : null;
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(leadSource: getEnumFromValue(LeadSource.values, selectedSource)));
  }

  void _initLeadSubSource(int? value, Emitter<AddLeadState> emit) {
    final initSelectedLeadSubSource = getLeadEntity?.enquiry?.subSource;
    final subSource = _allLeadSource.where((element) => element.leadSourceEntity?.isEnabled ?? false).firstWhereOrNull((e) => e.leadSourceEntity?.value == value)?.subSources?.map((e) => SelectableItem<String>(title: e, value: e, isSelected: initSelectedLeadSubSource == e)).toList();
    emit(state.copyWith(leadSubSource: subSource ?? [], selectedLeadSubSource: null, updateSubSource: false, errorMessage: ''));
    final selectedSubSource = subSource?.firstWhereOrNull((element) => element.isSelected);
    if (selectedSubSource != null) add(SelectLeadSubSourceEvent(selectedSubSource));
  }

  Future<void> _initSourcingManager(Emitter<AddLeadState> emit) async {
    final initSourcingManager = getLeadEntity?.sourcingManager?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var sourcingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSourcingManager == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => sourcingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSourcingManager == disabledUsers?.id, isEnabled: false)));
      sourcingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSourcingManager));
      emit(state.copyWith(sourcingManager: sourcingManagerUsers, errorMessage: ''));
      final selectedSourcingManager = sourcingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedSourcingManager != null) add(SelectSourcingManagerEvent(selectedSourcingManager));
    }
  }

  Future<void> _initClosingManager(Emitter<AddLeadState> emit) async {
    final initClosingManager = getLeadEntity?.closingManagerUser?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var closingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initClosingManager == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => closingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initClosingManager == disabledUsers?.id, isEnabled: false)));
      closingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initClosingManager));
      emit(state.copyWith(closingManager: closingManagerUsers, errorMessage: ''));
      final selectedClosingManager = closingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedClosingManager != null) add(SelectClosingManagerEvent(selectedClosingManager));
    }
  }

  FutureOr<void> _onSelectLeadSubSource(SelectLeadSubSourceEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(selectedLeadSubSource: event.selectedLeadSubSource, errorMessage: ''));
    final selectedSubSource = event.selectedLeadSubSource.isSelected ? event.selectedLeadSubSource.value : null;
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(subSource: selectedSubSource));
  }

  FutureOr<void> _onTogglePropertySubType(TogglePropertySubTypeEvent event, Emitter<AddLeadState> emit) async {
    final updatedPropertySubTypes = state.propertySubTypes.map((e) => e.value == event.selectedPropertySubType.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(propertySubTypes: updatedPropertySubTypes, errorMessage: ''));
    final propertyTypeIds = state.propertySubTypes.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(propertyTypeIds: propertyTypeIds));
    _initBhkAndBhkTypes(emit);
  }

  FutureOr<void> _onToggleNoOfBhk(ToggleNoOfBhkEvent event, Emitter<AddLeadState> emit) async {
    final updatedNoOfBhk = state.noOfBhk.map((e) => e.value == event.selectedNoOfBhk.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(noOfBhk: updatedNoOfBhk, errorMessage: ''));
    final selectedBhks = state.noOfBhk.where((element) => element.isSelected).map((bhk) => bhk.value).nonNulls.toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(bhks: selectedBhks));
  }

  FutureOr<void> _onToggleBhkType(ToggleBhkTypeEvent event, Emitter<AddLeadState> emit) async {
    final updatedNoOfBhkType = state.bhkTypes.map((e) => e.value == event.selectedBhkTypes.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(bhkTypes: updatedNoOfBhkType, errorMessage: ''));
    final selectedBhkTypes = state.bhkTypes.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(bhkTypes: selectedBhkTypes));
  }

  FutureOr<void> _onToggleProfession(ToggleProfessionEvent event, Emitter<AddLeadState> emit) async {
    if (event.selectedProfession == null) return;
    final updatedProfession = state.professions.map((e) => e.value == event.selectedProfession!.value ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    emit(state.copyWith(professions: updatedProfession, errorMessage: ''));
    final selectedProfession = event.selectedProfession!.isSelected ? null : event.selectedProfession!.value;
    _addLeadModel = _addLeadModel.copyWith(profession: selectedProfession);
  }

  FutureOr<void> _onSelectAgencyName(SelectAgencyNameEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(selectedAgencyNames: event.selectedAgencyName, errorMessage: ''));
    final selectedAgencyNames = event.selectedAgencyName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addLeadModel = _addLeadModel.copyWith(agencies: selectedAgencyNames);
  }

  FutureOr<void> _onSelectCampaignName(SelectCampaignNameEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(selectedCampaignNames: event.selectedCampaignName, errorMessage: ''));
    final selectedCampaignNames = event.selectedCampaignName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addLeadModel = _addLeadModel.copyWith(campaigns: selectedCampaignNames);
  }

  FutureOr<void> _onRemoveAgencyName(RemoveAgencyNameEvent event, Emitter<AddLeadState> emit) {
    final updatedSelectedAgency = state.selectedAgencyNames?.whereNot((element) => element.title == event.selectedAgencyName.title).toList();
    emit(state.copyWith(selectedAgencyNames: updatedSelectedAgency, errorMessage: ''));
    _addLeadModel.agencies?.removeWhere((element) => element.name == event.selectedAgencyName.title);
  }

  FutureOr<void> _onRemoveCampaignName(RemoveCampaignNameEvent event, Emitter<AddLeadState> emit) {
    final updatedSelectedCampaign = state.selectedCampaignNames?.whereNot((element) => element.title == event.selectedCampaignName.title).toList();
    emit(state.copyWith(selectedCampaignNames: updatedSelectedCampaign, errorMessage: ''));
    _addLeadModel.campaigns?.removeWhere((element) => element.name == event.selectedCampaignName.title);
  }

  FutureOr<void> _onSelectCarpetArea(SelectCarpetAreaEvent event, Emitter<AddLeadState> emit) async {
    if (event.selectedCarpetArea == null) return;
    emit(state.copyWith(selectedCarpetArea: event.selectedCarpetArea, errorMessage: ''));
    final selectedCarpetAreaValue = event.selectedCarpetArea!.isSelected ? event.selectedCarpetArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      carpetAreaUnitId: selectedCarpetAreaValue?.value?.id,
      conversionFactor: selectedCarpetAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectSaleableAreaEvent(SelectSaleableAreaEvent event, Emitter<AddLeadState> emit) async {
    if (event.selectedSaleableArea == null) return;
    emit(state.copyWith(selectedSaleableArea: event.selectedSaleableArea));
    final selectedSaleableValue = event.selectedSaleableArea!.isSelected ? event.selectedSaleableArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      saleableAreaUnitId: selectedSaleableValue?.value?.id,
      saleableAreaConversionFactor: selectedSaleableValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectBuiltUpAreaEvent(SelectBuiltUpAreaEvent event, Emitter<AddLeadState> emit) async {
    if (event.selectedBuiltUpArea == null) return;
    emit(state.copyWith(selectedBuiltUpArea: event.selectedBuiltUpArea, errorMessage: ''));

    final selectedBuiltUpValue = event.selectedBuiltUpArea!.isSelected ? event.selectedBuiltUpArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      builtUpAreaUnitId: selectedBuiltUpValue?.value?.id,
      builtUpAreaConversionFactor: selectedBuiltUpValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectSourcingManager(SelectSourcingManagerEvent event, Emitter<AddLeadState> emit) async {
    List<SelectableItem<String>>? updatedSourcingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedSourcingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedSourcingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedSourcingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedSourcingManager.removeWhere((element) => element.value == event.selectedSourcingManager.value);
    emit(state.copyWith(selectedSourcingManager: event.selectedSourcingManager, errorMessage: ''));
    _addLeadModel = _addLeadModel.copyWith(sourcingManager: event.selectedSourcingManager.value);
  }

  FutureOr<void> _onSelectClosingManager(SelectClosingManagerEvent event, Emitter<AddLeadState> emit) async {
    List<SelectableItem<String>>? updatedClosingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedClosingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedClosingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedClosingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedClosingManager.removeWhere((element) => element.value == event.selectedClosingManager.value);
    emit(state.copyWith(selectedClosingManager: event.selectedClosingManager, errorMessage: ''));
    _addLeadModel = _addLeadModel.copyWith(closingManager: event.selectedClosingManager.value);
  }

  FutureOr<void> _onAddCustomerLocation(AddCustomerLocationEvent event, Emitter<AddLeadState> emit) async {
    if (event.customerLocation == null) return;
    ItemSimpleModel<AddressModel> location;
    var locality = event.customerLocation?.locality ?? event.customerLocation?.subLocality ?? '';
    var subCommunity = event.customerLocation?.subCommunity ?? '';
    var community = event.customerLocation?.community ?? '';
    var towerName = event.customerLocation?.towerName ?? '';
    var city = event.customerLocation?.city ?? '';
    var state_ = event.customerLocation?.state ?? '';
    var country = event.customerLocation?.country ?? '';
    var postalCode = event.customerLocation?.postalCode ?? '';
    String title = [locality, subCommunity, community, towerName, city, state_, country, postalCode].where((element) => element.isNotEmpty).join(', ');
    location = ItemSimpleModel<AddressModel>(title: title, value: event.customerLocation);
    emit(state.copyWith(customerLocations: location));
    _addLeadModel = _addLeadModel.copyWith(address: state.customerLocations?.value);
  }

  FutureOr<void> _onRemoveCustomerLocation(RemoveCustomerLocationEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(customerLocations: ItemSimpleModel<AddressModel>(title: 'None')));
    _addLeadModel = _addLeadModel.copyWith(address: state.customerLocations?.value);
  }

  void _initBhkAndBhkTypes(Emitter<AddLeadState> emit) {
    final initSelectedNoOfBhk = getLeadEntity?.enquiry?.bHKs?.nonNulls;
    final initSelectedBhkTypes = getLeadEntity?.enquiry?.bHKTypes?.nonNulls;

    final selectedPropertyType = state.propertyTypes.firstWhereOrNull((element) => element.isSelected);
    final isOnlyPlotSelected = state.propertySubTypes.firstWhereOrNull((element) => element.title.toLowerCase() == "plot")?.isSelected ?? false;
    final isAnyOtherSelected = state.propertySubTypes.any((element) => element.title.toLowerCase() != "plot" && element.isSelected);
    final isOnlyPlotSelectedAndNoOthers = isOnlyPlotSelected && !isAnyOtherSelected;
    final isPropertySubTypesSelected = state.propertySubTypes.any((element) => element.isSelected);
    if (!isPropertySubTypesSelected || selectedPropertyType == null) {
      emit(state.copyWith(noOfBhk: [], bhkTypes: [], errorMessage: ''));
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(bhkTypes: [], bhks: []));
    } else if (selectedPropertyType.value == PropertyType.residential && isPropertySubTypesSelected && !isOnlyPlotSelectedAndNoOthers) {
      if (!state.noOfBhk.any((element) => element.isSelected)) {
        final noOfBhk = NoOfBHK.values.map((bhk) => ItemSimpleModel<double>(title: bhk.description, value: bhk.noOfBhk, isSelected: initSelectedNoOfBhk?.contains(bhk.noOfBhk) ?? false)).toList();
        final bhkTypes = BHKType.values.map((type) => ItemSimpleModel<BHKType>(title: type.description, value: type, isSelected: initSelectedBhkTypes?.contains(type) ?? false)).toList();
        bhkTypes.removeWhere((element) => element.value == BHKType.none);
        final selectedBhkIndex = noOfBhk.lastIndexWhere((element) => element.isSelected);
        emit(state.copyWith(noOfBhk: noOfBhk, bhkTypes: bhkTypes, isNoOfBhkExpanded: selectedBhkIndex > 3));
        final selectedBhks = state.noOfBhk.where((element) => element.isSelected).map((bhk) => bhk.value).nonNulls.toList();
        final selectedBhkTypes = state.bhkTypes.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
        _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(bhkTypes: selectedBhkTypes, bhks: selectedBhks));
      }
    } else {
      emit(state.copyWith(noOfBhk: [], bhkTypes: [], errorMessage: ''));
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(bhkTypes: [], bhks: []));
    }
  }

  FutureOr<void> _onSelectProperties(SelectPropertiesEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(selectedProperties: event.selectedProperties, errorMessage: ''));
    final selectedProperties = event.selectedProperties.map((e) => e.title).toList();
    _addLeadModel = _addLeadModel.copyWith(propertiesList: selectedProperties);
  }

  FutureOr<void> _onRemoveProperty(RemovePropertyEvent event, Emitter<AddLeadState> emit) {
    final updatedSelectedProperties = state.selectedProperties?.whereNot((element) => element.title == event.selectedProperty.title).toList();
    emit(state.copyWith(selectedProperties: updatedSelectedProperties, errorMessage: ''));
    _addLeadModel.propertiesList?.removeWhere((title) => title == event.selectedProperty.title);
  }

  FutureOr<void> _onSelectProjects(SelectProjectsEvent event, Emitter<AddLeadState> emit) async {
    List<SelectableItem<LeadProjectUnitModel>>? selectableProjectUnit = [];
    final projectUnits = await leadProjectUnitUseCase(SelectedProjectModel(selectedProjects: event.selectedProjects.map((e) => e.title).toList()));

    projectUnits.fold(
      (l) => LeadratCustomSnackbar.show(message: "cannot able to fetch unit information", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error),
      (r) {
        if ((r?.isEmpty ?? false) && (state.selectedProjectsUnits?.isEmpty ?? false)) {
          LeadratCustomSnackbar.show(message: "no unit information available for selected projects", navigatorKey: MyApp.navigatorKey, type: SnackbarType.success);
          return;
        }
        r?.values.forEach(
          (element) {
            for (LeadProjectUnitModel action in element) {
              selectableProjectUnit.add(SelectableItem(title: action.unitName ?? '', value: action));
            }
          },
        );
      },
    );
    final selectedProjectUnit = selectableProjectUnit.where((e) => state.selectedProjectsUnits?.contains(e.value?.unitId) ?? false).toList();
    emit(state.copyWith(selectedProjects: event.selectedProjects, errorMessage: '', selectableProjectUnit: selectableProjectUnit, selectedProjectsUnits: selectedProjectUnit));
    final selectedProjects = event.selectedProjects.map((e) => e.title).toList();
    _addLeadModel = _addLeadModel.copyWith(projectsList: selectedProjects);
    _addLeadModel = _addLeadModel.copyWith(unitIds: selectedProjectUnit.map((e) => e.value?.unitId ?? '').toList());
  }

  FutureOr<void> _onRemoveProjects(RemoveProjectsEvent event, Emitter<AddLeadState> emit) async {
    final updatedSelectedProjects = state.selectedProjects?.whereNot((element) => element.title == event.selectedProjects.title).toList();
    List<SelectableItem<LeadProjectUnitModel>>? selectableProjectUnit = [];
    final projectUnits = await leadProjectUnitUseCase(SelectedProjectModel(selectedProjects: updatedSelectedProjects?.map((e) => e.title).toList()));

    projectUnits.fold(
      (l) => LeadratCustomSnackbar.show(message: "cannot able to fetch unit information", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error),
      (r) {
        r?.values.forEach(
          (element) {
            for (LeadProjectUnitModel action in element) {
              selectableProjectUnit.add(SelectableItem(title: action.unitName ?? '', value: action));
            }
          },
        );
      },
    );
    final selectedProjectUnit = selectableProjectUnit.where((e) => state.selectedProjectsUnits?.contains(e.value?.unitId) ?? false).toList();
    emit(state.copyWith(selectedProjects: updatedSelectedProjects, errorMessage: '', selectableProjectUnit: selectableProjectUnit, selectedProjectsUnits: selectedProjectUnit));
    _addLeadModel.projectsList?.removeWhere((title) => title == event.selectedProjects.title);
    _addLeadModel = _addLeadModel.copyWith(unitIds: selectedProjectUnit.map((e) => e.value?.unitId ?? '').toList());
  }

  FutureOr<void> _onSelectAssignedUser(SelectAssignedUserEvent event, Emitter<AddLeadState> emit) async {
    List<SelectableItem<String>>? updatedSecondaryUsers;
    if (globalSettings?.isDualOwnershipEnabled ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      updatedSecondaryUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedSecondaryUsers?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
      updatedSecondaryUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
      updatedSecondaryUsers.removeWhere((element) => element.value == event.selectedUser.value);
    }
    emit(state.copyWith(selectedAssignedUser: event.selectedUser, errorMessage: '', secondaryUsers: updatedSecondaryUsers));
    _addLeadModel = _addLeadModel.copyWith(assignTo: event.selectedUser.value);
  }

  FutureOr<void> _onSelectSecondaryUser(SelectSecondaryUserEvent event, Emitter<AddLeadState> emit) async {
    if (globalSettings?.isDualOwnershipEnabled ?? false) {
      if (state.selectedAssignedUser == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: "selected assign to user first"));
        return;
      }
      final currentUser = _usersDataRepository.getLoggedInUser();
      final updatedAssignedUser = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedAssignedUser.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
      updatedAssignedUser.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
      updatedAssignedUser.removeWhere((element) => element.value == event.selectedUser.value);
      emit(state.copyWith(selectedSecondaryAssignedUser: event.selectedUser, errorMessage: '', assignToUsers: updatedAssignedUser));
      _addLeadModel = _addLeadModel.copyWith(secondaryUserId: event.selectedUser.value);
    }
  }

  FutureOr<void> _onToggleSubTypesExpanded(ToggleSubTypesExpandedEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(isSubTypesExpanded: !state.isSubTypesExpanded, errorMessage: ''));
  }

  FutureOr<void> _onToggleNoOfBhkExpanded(ToggleNoOfBhkExpandedEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(isNoOfBhkExpanded: !state.isNoOfBhkExpanded, errorMessage: ''));
  }

  void _emitFailureState(Emitter<AddLeadState> emit, String errorMessage) => emit(state.copyWith(
        errorMessage: errorMessage,
        pageState: PageState.failure,
      ));

  FutureOr<void> _onResetState(ResetStateEvent event, Emitter<AddLeadState> emit) {
    emit(state.initialState());
  }

  FutureOr<void> _onToggleReferralFields(ToggleReferralFieldsEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(isReferralDetailsVisible: !state.isReferralDetailsVisible, errorMessage: ''));
    if (!state.isReferralDetailsVisible) {
      referralEmailController?.text = "";
      referralNameController?.text = (referralPhoneController?.text = "") ?? "";
    }
  }

  FutureOr<void> _onToggleAltPhoneField(ToggleAltPhoneFieldEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(isAltPhoneFieldVisible: event.hideAltPhoneField ?? !state.isAltPhoneFieldVisible, errorMessage: '', altContactNumber: ''));
    if (!state.isAltPhoneFieldVisible) altPhoneController?.text = "";
  }

  FutureOr<void> _onToggleEmailField(ToggleEmailFieldEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(isEmailFieldVisible: !state.isEmailFieldVisible, errorMessage: ''));
    if (!state.isEmailFieldVisible) emailController?.text = "";
  }

  FutureOr<void> _onTogglePossessionDate(TogglePossessionDateEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(isPossessionDateVisible: !state.isPossessionDateVisible, errorMessage: '', updatePossessionDate: state.isPossessionDateVisible));
  }

  FutureOr<void> _onSelectPossessionDate(SelectPossessionDateEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(possessionDate: event.selectedDate));
  }

  FutureOr<void> _onCreateLead(CreateLeadEvent event, Emitter<AddLeadState> emit) async {
    if (state.pageState == PageState.loading) return;
    if ((state.globalSettingModel?.leadNotesSetting?.isNotesMandatoryEnabled ?? false) && (state.globalSettingModel?.leadNotesSetting?.isNotesMandatoryOnAddLead ?? false)) {
      if (notesController?.text.isEmpty ?? true) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'notes is mandatory field'));
        return;
      }
    }
    if (leadNameController?.text.trim().isEmpty ?? false) {
      _emitFailureState(emit, "Please enter a lead name");
      return;
    }
    if (state.contactNumber?.isEmpty ?? false) {
      _emitFailureState(emit, "Please enter a contact number");
      return;
    }
    if (state.contactNumber?.trim() == state.altContactNumber?.trim()) {
      emit(state.copyWith(pageState: PageState.failure, errorMessage: 'primary and alternative contact number cannot be same'));
      return;
    }
    if (minBudgetController?.text.isNotEmpty ?? false) {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(lowerBudget: int.tryParse(minBudgetController?.text ?? '0')));
    }
    if (maxBudgetController?.text.isNotEmpty ?? false) {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(upperBudget: int.tryParse(maxBudgetController?.text ?? '0')));
    }
    final isPropertyTypeSelected = state.propertyTypes.any((element) => element.isSelected);
    final isPropertySubTypeSelected = state.propertySubTypes.any((element) => element.isSelected);
    if (isPropertyTypeSelected && !isPropertySubTypeSelected) {
      _emitFailureState(emit, "Please select property sub-type");
      return;
    }
    List<String>? paymentPlans;

    final String? plan1 = paymentPlan1Controller?.text.trim();
    final String? plan2 = paymentPlan2Controller?.text.trim();

    final int? plan1Value = (plan1?.isNotEmpty ?? false) ? int.tryParse(plan1 ?? '') : null;
    final int? plan2Value = (plan2?.isNotEmpty ?? false) ? int.tryParse(plan2 ?? '') : null;

    if ((plan1?.isNotEmpty == true && plan1Value == null) || (plan2?.isNotEmpty == true && plan2Value == null)) {
      _emitFailureState(emit, "Please enter proper payment plan");
      return;
    }
    if ((plan1?.isNotEmpty == true) ^ (plan2?.isNotEmpty == true)) {
      LeadratCustomSnackbar.show(message: "Please fill both payment plan values.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return;
    }

    if ((plan1Value != null && (plan1Value < 0 || plan1Value > 100)) || (plan2Value != null && (plan2Value < 0 || plan2Value > 100))) {
      _emitFailureState(emit, "Invalid % value");
      return;
    }

    if (plan1Value != null && plan2Value != null && plan1Value + plan2Value != 100) {
      _emitFailureState(emit, "Sum of payment plans must be 100");
      return;
    }

    final plans = <String>[];
    if (plan1Value != null) plans.add(plan1 ?? '0');
    if (plan2Value != null) plans.add(plan2 ?? '0');

    paymentPlans = plans.isEmpty ? null : plans;

    _addLeadModel = _addLeadModel.copyWith(
      alternateContactNo: state.altContactNumber.isNotNullOrEmpty() ? state.altContactNumber?.trim() : null,
      email: (emailController?.text.isEmpty ?? true) ? null : emailController?.text.trim(),
      notes: (notesController?.text.isEmpty ?? true) ? null : notesController?.text.trim(),
      companyName: (companyNameController?.text.isEmpty ?? true) ? null : companyNameController?.text.trim(),
      referralName: (referralNameController?.text.isEmpty ?? true) ? null : referralNameController?.text.trim(),
      referralContactNo: state.referralContact?.trim(),
      designation: (designationController?.text.isEmpty ?? true) ? null : designationController?.text.trim(),
      landLine: (landlineNumberController?.text.isEmpty ?? true) ? null : landlineNumberController?.text.trim(),
      enquiry: _addLeadModel.enquiry?.copyWith(
        buyer: state.selectedBuyer?.value,
        paymentPlans: paymentPlans,
        possessionDate: state.possessionDate?.getBasedOnTimeZone()?.toUtcFormat(),
      ),
      confidentialNotes: getLeadEntity?.confidentialNotes,
      duplicateLeadVersion: getLeadEntity?.duplicateLeadVersion,
      additionalProperties: getLeadEntity?.additionalProperties,
      referralEmail: (referralEmailController?.text.isNotEmpty ?? false) ? referralEmailController?.text.trim() : null,
      channelPartnerExecutiveName: (executiveNameController?.text.isEmpty ?? true) ? null : executiveNameController?.text.trim(),
      channelPartnerContactNo: state.executiveContact?.trim(),
      maritalStatus: state.selectedMaritalStatus?.value,
      gender: state.selectedGender?.value,
      removeMaritalStatus: state.selectedMaritalStatus == null,
      removeGender: state.selectedGender == null,
      dateOfBirth: state.dateOfBirth.toUtcFormat(),
      bookedBy: getLeadEntity?.bookedBy,
      bookedUnderName: getLeadEntity?.bookedUnderName,
      chosenProperty: getLeadEntity?.chosenProperty,
      soldPrice: getLeadEntity?.soldPrice,
      chosenProject: getLeadEntity?.chosenProject,
      bookedDate: getLeadEntity?.bookedDate,
      anniversaryDate: state.anniversaryDate.toUtcFormat(),
    );

    // if (!_validatePropertyAreas()) {
    //   return;
    // }
    if (carpetAreaController?.text.isNotEmpty ?? false) {
      var carpetValue = double.parse(carpetAreaController!.text);
      if (state.selectedCarpetArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the carpet unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(carpetArea: carpetValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetCarpetAreaUnit: true));
    }

    if (saleableAreaAreaController?.text.isNotEmpty ?? false) {
      var saleableValue = double.parse(saleableAreaAreaController!.text);
      if (state.selectedSaleableArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the saleable unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(saleableArea: saleableValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetSaleableAreaUnit: true));
    }

    if (builtUpAreaController?.text.isNotEmpty ?? false) {
      var builtUpValue = double.parse(builtUpAreaController!.text);
      if (state.selectedBuiltUpArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the builtUp unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(builtUpArea: builtUpValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetBuiltUpAreaAreaUnit: true));
    }
    _addLeadModel = _addLeadModel.copyWith(name: leadNameController?.text.trim() ?? "", contactNo: state.contactNumber?.trim() ?? "");
    if (state.possessionTypeSelectedItem != null) {
      _addLeadModel = _addLeadModel.copyWith(possesionType: state.possessionTypeSelectedItem?.value);

      _addLeadModel = _addLeadModel.copyWith(possesionType: state.possessionTypeSelectedItem?.value);
    }
    if (isEditing) {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "updating lead", showDialogProgress: true));
      var statusId = getLeadEntity != null && getLeadEntity?.status?.childType != null ? getLeadEntity?.status?.childType?.id : getLeadEntity?.status?.id;
      _addLeadModel = _addLeadModel.copyWith(leadStatusId: statusId, id: getLeadEntity?.id, serialNumber: getLeadEntity?.serialNumber);
      final updateLead = await _updateLeadUseCase(_addLeadModel.toUpdateLeadModel());
      updateLead.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        if (success != null) {
          getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent(success));
          getIt<LeadInfoBloc>().add(InitializeHistoryEvent(true));
          getIt<LeadInfoBloc>().add(InitializeNotesEvent(true));
          emit(state.copyWith(pageState: PageState.success, errorMessage: '', successMessage: "Lead edited successfully", showDialogProgress: false));
          getIt<ManageLeadsBloc>().add(ResetManageLeadsBlocEvent());
        }
      });
    } else {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "saving lead", showDialogProgress: true));
      final addLead = await _addLeadUseCase(_addLeadModel);
      addLead.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        emit(state.copyWith(pageState: PageState.success, errorMessage: '', successMessage: "Lead added successfully", showDialogProgress: false));
      });
    }
  }

  Future<void> _initInitialData(Emitter<AddLeadState> emit) async {
    leadNameController?.text = getLeadEntity?.name ?? '';
    phoneController?.text = getLeadEntity?.contactNo ?? '';
    emailController?.text = getLeadEntity?.email ?? '';
    altPhoneController?.text = getLeadEntity?.alternateContactNo ?? '';
    referralNameController?.text = getLeadEntity?.referralName ?? '';
    referralPhoneController?.text = getLeadEntity?.referralContactNo ?? '';
    designationController?.text = getLeadEntity?.designation ?? '';
    notesController?.text = getLeadEntity?.notes ?? '';
    companyNameController?.text = getLeadEntity?.companyName ?? '';
    landlineNumberController?.text = getLeadEntity?.landline ?? '';
    referralEmailController?.text = getLeadEntity?.referralEmail ?? '';
    executiveNameController?.text = getLeadEntity?.channelPartnerExecutiveName ?? '';
    executivePhoneController?.text = getLeadEntity?.channelPartnerContactNo ?? '';
    final paymentPlans = getLeadEntity?.enquiry?.paymentPlans ?? [];
    paymentPlan1Controller?.text = paymentPlans.isNotEmpty ? paymentPlans[0] : '';
    paymentPlan2Controller?.text = paymentPlans.length > 1 ? paymentPlans[1] : '';
    final buyer = getLeadEntity?.enquiry?.buyer;
    if (buyer != null) {
      final selectableSelectedBuyers = SelectableItem<BuyerType>(title: buyer.description ?? '', value: buyer);
      add(SelectBuyerEvent(selectableSelectedBuyers));
    }
    if (getLeadEntity?.enquiry?.upperBudget != null && getLeadEntity!.enquiry!.upperBudget! > 0) {
      maxBudgetController?.text = getLeadEntity?.enquiry!.upperBudget!.toString() ?? '';
    }
    if (getLeadEntity?.enquiry?.lowerBudget != null && getLeadEntity!.enquiry!.lowerBudget! > 0) {
      minBudgetController?.text = getLeadEntity?.enquiry!.lowerBudget!.toString() ?? '';
    }
    if (getLeadEntity?.enquiry?.carpetArea != null && getLeadEntity!.enquiry!.carpetArea! > 0.0) {
      carpetAreaController?.text = getLeadEntity?.enquiry!.carpetArea!.toString().replaceAll('.0', '') ?? '';
    }
    if (getLeadEntity?.enquiry?.saleableArea != null && getLeadEntity!.enquiry!.saleableArea! > 0.0) {
      saleableAreaAreaController?.text = getLeadEntity?.enquiry?.saleableArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getLeadEntity?.enquiry?.builtUpArea != null && getLeadEntity!.enquiry!.builtUpArea! > 0.0) {
      builtUpAreaController?.text = getLeadEntity?.enquiry?.builtUpArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getLeadEntity?.gender != null && getLeadEntity?.gender != GenderEnum.notMentioned) {
      var selectedGender = _genders.firstWhereOrNull((element) => element.value == getLeadEntity?.gender);
      if (selectedGender != null) add(SelectGenderEvent(selectedGender.copyWith(isSelected: true)));
    }
    if (getLeadEntity?.maritalStatus != null && getLeadEntity?.maritalStatus != MaritalStatusType.notMentioned) {
      var selectedMaritalStatus = _maritalStatus.firstWhereOrNull((element) => element.value == getLeadEntity?.maritalStatus);
      if (selectedMaritalStatus != null) add(SelectMaritalStatusEvent(selectedMaritalStatus.copyWith(isSelected: true)));
    }
    if (getLeadEntity?.dateOfBirth != null) {
      add(SelectDateOfBirthEvent(getLeadEntity!.dateOfBirth!));
    }
    if (getLeadEntity?.anniversaryDate != null) {
      add(SelectAnniversaryDateEvent(getLeadEntity!.anniversaryDate!));
    }

    final initSelectedLocations = getLeadEntity?.enquiry?.addresses;
    if (initSelectedLocations != null) {
      final locations = initSelectedLocations
          .map((e) {
            final title = (e.subLocality?.isNotEmpty == true) ? e.subLocality : [e.locality, e.state].where((s) => s?.isNotEmpty == true).join(', ');
            if (title.isNotNullOrEmpty()) {
              return ItemSimpleModel<AddressModel>(
                title: title ?? '',
                value: e.toModel(),
              );
            }
          })
          .nonNulls
          .toList();
      emit(state.copyWith(locations: locations));
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(addresses: locations.map((e) => e.value).nonNulls.toList()));
    }

    final initSelectedCustomerLocation = getLeadEntity?.address;
    if (initSelectedCustomerLocation != null) {
      final customerLocation = ItemSimpleModel<AddressModel>(title: '${initSelectedCustomerLocation.subLocality ?? ''}, ${initSelectedCustomerLocation.locality ?? ''}, ${initSelectedCustomerLocation.state ?? ''}, ${initSelectedCustomerLocation.city ?? ''}, ${initSelectedCustomerLocation.country ?? ''}', value: initSelectedCustomerLocation);
      emit(state.copyWith(customerLocations: customerLocation));
      _addLeadModel = _addLeadModel.copyWith(address: customerLocation.value);
    }

    emit(state);
  }

  FutureOr<void> _onAddLocation(AddLocationEvent event, Emitter<AddLeadState> emit) async {
    if (event.location == null) return;
    List<ItemSimpleModel<AddressModel>> locations = [];
    var locality = event.location?.locality ?? event.location?.subLocality ?? '';
    var subCommunity = event.location?.subCommunity ?? '';
    var community = event.location?.community ?? '';
    var towerName = event.location?.towerName ?? '';
    var city = event.location?.city ?? '';
    var state_ = event.location?.state ?? '';
    var country = event.location?.country ?? '';
    var postalCode = event.location?.postalCode ?? '';
    String title = [locality, subCommunity, community, towerName, city, state_, country, postalCode].where((element) => element.isNotEmpty).join(', ');
    locations.add(ItemSimpleModel<AddressModel>(title: title, value: event.location));
    emit(state.copyWith(locations: [...locations, ...state.locations]));
    final selectedAddresses = state.locations.map((e) => e.value).nonNulls.toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  FutureOr<void> _onRemoveLocation(RemoveLocationEvent event, Emitter<AddLeadState> emit) {
    final updatedLocations = state.locations.whereNot((element) => element.value == event.selectedItem.value).toList();
    emit(state.copyWith(locations: updatedLocations));
    final selectedAddresses = updatedLocations.map((e) => e.value).nonNulls.toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  void _onSelectBuyer(SelectBuyerEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(selectedBuyer: event.selectedBuyer));
  }

  FutureOr<void> _onCheckLeadContactAlreadyExists(CheckLeadContactAlreadyExistsEvent event, Emitter<AddLeadState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";

    // When editing, allow country code changes for the same phone number
    if (isEditing && getLeadEntity?.contactNo != null) {
      // If the full contact number matches exactly, return early
      if (contactWithCountryCode == getLeadEntity?.contactNo) return;

      // Extract phone number parts from both new and existing contact numbers
      final newPhoneParts = PhoneUtils.processPhoneNumber(contactWithCountryCode);
      final existingPhoneParts = PhoneUtils.processPhoneNumber(getLeadEntity!.contactNo!);

      // If both have valid phone parts and the phone numbers (without country code) are the same,
      // allow the country code change without triggering duplicate check
      if (newPhoneParts.$1 != null && newPhoneParts.$2 != null && existingPhoneParts.$1 != null && existingPhoneParts.$2 != null && newPhoneParts.$2 == existingPhoneParts.$2) {
        // Update the contact number in state and return early
        emit(state.copyWith(contactNumber: contactWithCountryCode, isLeadAlreadyExits: false));
        return;
      }
    }

    final checkIfLeadContactExists = await _getLeadByContactNoUseCase(GetLeadByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    checkIfLeadContactExists.fold(
      (failure) => emit(state.copyWith(isLeadAlreadyExits: false, contactNumber: contactWithCountryCode, canNavigateToLeadInfo: false)),
      (leadContact) {
        if (leadContact != null && _usersDataRepository.checkHasPermission(AppModule.lead, CommandType.createDuplicateLeads) && (globalSettings?.duplicateLeadFeatureInfo?.isFeatureAdded ?? false) && (globalSettings?.duplicateLeadFeatureInfo?.isSourceBased ?? false)) {
          if (!(state.isDuplicateLeadBottomSheetVisible)) {
            if (leadContact.id == getLeadEntity?.id) return;
            emit(state.copyWith(isDuplicateLeadBottomSheetVisible: (leadContact.canNavigate ?? false) || leadContact.id != null, existingLeadId: leadContact.id, contactNumber: contactWithCountryCode, primaryOrSecondary: 'primary', canNavigateToLeadInfo: leadContact.canNavigate));
          }
          if (leadContact.canNavigate ?? false) {
            add(DuplicateSourceCheck());
          }
        } else if (leadContact != null && !(state.isLeadAlreadyExits)) {
          final canNavigate = leadContact.canNavigate ?? false;
          LeadratCustomSnackbar.show(message: "Lead already exists with this number.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
          emit(state.copyWith(isLeadAlreadyExits: canNavigate || leadContact.id != null, existingLeadId: leadContact.id, contactNumber: contactWithCountryCode, canNavigateToLeadInfo: canNavigate));
        } else if (leadContact == null) {
          emit(state.copyWith(isLeadAlreadyExits: false, contactNumber: contactWithCountryCode, isDuplicateLeadBottomSheetVisible: false, existingLeadId: '', canNavigateToLeadInfo: false));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.lead, CommandType.viewAllLeads))) {
          if (state.existingLeadId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onCheckAltContactAlreadyExists(CheckAltContactAlreadyExistsEvent event, Emitter<AddLeadState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";

    if ((isEditing && contactWithCountryCode == getLeadEntity?.alternateContactNo || contactWithCountryCode == getLeadEntity?.contactNo)) return;

    final checkIfLeadContactExists = await _getLeadByContactNoUseCase(GetLeadByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    checkIfLeadContactExists.fold(
      (failure) => null,
      (leadContact) {
        if (leadContact != null && _usersDataRepository.checkHasPermission(AppModule.lead, CommandType.createDuplicateLeads) && (globalSettings?.duplicateLeadFeatureInfo?.isFeatureAdded ?? false) && (globalSettings?.duplicateLeadFeatureInfo?.isSourceBased ?? false)) {
          if (!(state.isDuplicateLeadBottomSheetVisibleForAltNumber)) {
            if (leadContact.id == getLeadEntity?.id) return;
            final canNavigate = leadContact.canNavigate ?? false;
            emit(state.copyWith(isDuplicateLeadBottomSheetVisibleForAltNumber: canNavigate || leadContact.id != null, existingLeadId: leadContact.id, primaryOrSecondary: 'alternative', canNavigateToLeadInfo: canNavigate));
          }
          if (leadContact.canNavigate ?? false) {
            add(DuplicateSourceCheck());
          }
        } else if (leadContact != null && !(state.isLeadAlreadyExitsOnAltNumber)) {
          final canNavigate = leadContact.canNavigate ?? false;
          LeadratCustomSnackbar.show(message: "Lead already exists with this number.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
          emit(state.copyWith(isLeadAlreadyExitsOnAltNumber: canNavigate || leadContact.id != null, existingLeadId: leadContact.id, canNavigateToLeadInfo: canNavigate));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.lead, CommandType.viewAllLeads))) {
          if (state.existingLeadId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onOnLeadContactChanged(OnLeadContactChangedEvent event, Emitter<AddLeadState> emit) {
    final leadContact = "+${event.countryCode}${event.contactNumber}";
    emit(state.copyWith(contactNumber: leadContact, isLeadAlreadyExits: false, isDuplicateLeadBottomSheetVisible: false, primaryOrSecondary: '', canNavigateToLeadInfo: false));
  }

  FutureOr<void> _onOnAltContactChanged(OnAltContactChangedEvent event, Emitter<AddLeadState> emit) {
    final altContact = "+${event.countryCode}${event.contactNumber}";
    emit(state.copyWith(altContactNumber: altContact, isLeadAlreadyExitsOnAltNumber: false, isDuplicateLeadBottomSheetVisibleForAltNumber: false, primaryOrSecondary: '', canNavigateToLeadInfo: false));
  }

  FutureOr<void> _onOnReferralContactChanged(OnReferralContactChangedEvent event, Emitter<AddLeadState> emit) {
    final referralContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(referralContact: referralContact));
  }

  FutureOr<void> _onExecutiveContactChanged(OnExecutiveContactChangedEvent event, Emitter<AddLeadState> emit) {
    final executiveContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(executiveContact: executiveContact));
  }

  FutureOr<void> _onPickContact(PickContactEvent event, Emitter<AddLeadState> emit) async {
    final contact = await PhoneUtils.pickContact();
    if (contact != null && contact.fullName != null && contact.phoneNumber != null) {
      leadNameController?.text = contact.fullName ?? '';
      final contactNumber = contact.phoneNumber?.number.toString().replaceAll(' ', '').replaceAll('-', '').trim() ?? '';
      phoneController?.text = contactNumber;
    }
  }

  FutureOr<void> _onCurrencySelect(SelectCurrency event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(selectedCurrency: event.selectedCurrency));
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(currency: event.selectedCurrency.value));
  }

  FutureOr<void> _onSelectedChannelPartners(SelectChannelPartnerEvent event, Emitter<AddLeadState> emit) async {
    final selectedChannelPartners = event.selectedChannelPartners.map((e) => e.title).toList();
    emit(state.copyWith(selectedChannelPartners: event.selectedChannelPartners));
    _addLeadModel = _addLeadModel.copyWith(channelPartnerList: selectedChannelPartners);
  }

  FutureOr<void> _onRemoveChannelPartnerName(RemoveChannelPartnerNameEvent event, Emitter<AddLeadState> emit) {
    final updatedSelectedChannelPartners = state.selectedChannelPartners.whereNot((element) => element.title == event.selectedChannelPartner.title).toList();
    emit(state.copyWith(selectedChannelPartners: updatedSelectedChannelPartners, errorMessage: ''));
    _addLeadModel.channelPartners?.removeWhere((element) => element.firmName == event.selectedChannelPartner.title);
    _addLeadModel.channelPartnerList?.removeWhere((element) => element == event.selectedChannelPartner.title);
  }

  bool _validatePropertyAreas() {
    double? saleableArea = (saleableAreaAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(saleableAreaAreaController!.text) : null;
    double? builtUpArea = (builtUpAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(builtUpAreaController!.text) : null;
    double? carpetArea = (carpetAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(carpetAreaController!.text) : null;
    if (saleableArea != null && builtUpArea != null && saleableArea <= builtUpArea) {
      LeadratCustomSnackbar.show(message: "Built-up Area should be less than Saleable Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (builtUpArea != null && carpetArea != null && builtUpArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: "Carpet Area should be less than Built-up Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && carpetArea != null && saleableArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: "Carpet Area should be less than Saleable Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    return true;
  }

  FutureOr<void> _onSelectPurpose(SelectPurposeEvent event, Emitter<AddLeadState> emit) async {
    if (event.selectedPurpose == null) return;
    emit(state.copyWith(selectedPurpose: event.selectedPurpose));
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(purpose: state.selectedPurpose?.value));
  }

  void _initPurpose(Emitter<AddLeadState> emit) {
    final initialSelectedPurpose = getLeadEntity?.enquiry?.purpose;
    final purposes = PurposeEnum.values.where((element) => element != PurposeEnum.none).map((e) => SelectableItem<PurposeEnum>(title: e.description, value: e));
    emit(state.copyWith(purposes: purposes.toList()));
    if (purposes.isNotEmpty) add(SelectPurposeEvent(purposes.firstWhereOrNull((element) => element.value == initialSelectedPurpose)));
  }

  FutureOr<void> _onAssignedToLoggedInUser(AssignedToLoggedInUser event, Emitter<AddLeadState> emit) async {
    final response = await _checkLeadAssignedByLeadIdUseCase.call(state.existingLeadId ?? '');
    response.fold((failure) => {}, (result) {
      emit(state.copyWith(isAssignedLoggedInUser: result));
    });
  }

  FutureOr<void> _onSelectGender(SelectGenderEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(selectedGender: event.selectedGender, resetGender: !event.selectedGender.isSelected));
  }

  FutureOr<void> _onSelectMaritalStatus(SelectMaritalStatusEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(selectedMaritalStatus: event.selectedMaritalStatus, resetMaritalStatus: !event.selectedMaritalStatus.isSelected));
  }

  FutureOr<void> _onSelectDateOfBirth(SelectDateOfBirthEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(dateOfBirth: event.dateOfBirth));
  }

  FutureOr<void> _onSelectAnniversaryDate(SelectAnniversaryDateEvent event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(anniversaryDate: event.anniversaryDate));
  }

  FutureOr<void> _onSelectPossessionType(SelectPossessionType event, Emitter<AddLeadState> emit) async {
    emit(state.copyWith(
      possessionTypeSelectedItem: event.selectPossessionType,
      isPossessionDateCustomSelected: event.selectPossessionType?.value == PossessionType.customDate,
      possessionDate: DateTimeUtils.getPossessionDate(event.selectPossessionType?.value ?? PossessionType.none),
      updatePossessionDate: PossessionType.underConstruction == event.selectPossessionType?.value,
      isPossessionDateVisible: event.selectPossessionType != null,
    ));
  }

  void _initPossessionType(Emitter<AddLeadState> emit) {
    PossessionType? initialPossessionType = getLeadEntity?.possesionType;
    initialPossessionType ??= getLeadEntity?.enquiry?.possesionType;
    if (getLeadEntity?.enquiry?.possessionDate != null) initialPossessionType ??= PossessionType.customDate;
    List<SelectableItem<PossessionType?>> possessionTypeItem = PossessionType.values.where((e) => e.description != 'None').map((e) => SelectableItem<PossessionType?>(title: e.description, value: e)).toList();
    emit(state.copyWith(possessionTypeSelectableItems: possessionTypeItem, possessionDate: getLeadEntity?.enquiry?.possessionDate));
    if (possessionTypeItem.isNotEmpty) add(SelectPossessionType(possessionTypeItem.firstWhereOrNull((element) => element.value == initialPossessionType)));
  }

  FutureOr<void> _onDuplicateSourceCheck(DuplicateSourceCheck event, Emitter<AddLeadState> emit) {
    if (globalSettings?.duplicateLeadFeatureInfo?.isFeatureAdded == true && (globalSettings?.duplicateLeadFeatureInfo?.sources?.isNotEmpty ?? false) && state.selectedLeadSource != null && state.contactNumber.isNotNullOrEmpty() && (state.isDuplicateLeadBottomSheetVisible || state.isDuplicateLeadBottomSheetVisibleForAltNumber)) {
      final sourceList = globalSettings?.duplicateLeadFeatureInfo?.sources?.map((e) => e.value).toList();

      final isAllowed = sourceList?.contains(state.selectedLeadSource?.value) ?? false;

      if (!isAllowed) {
        emit(state.copyWith(isSelectedSourceNotAllowed: true));
        LeadratCustomSnackbar.show(
          message: "No duplicate lead is allowed for the selected source",
          navigatorKey: MyApp.navigatorKey,
          type: SnackbarType.warning,
        );
      } else {
        emit(state.copyWith(isSelectedSourceNotAllowed: false));
      }
    }
  }

  FutureOr<void> _onSelectProjectUnitEvent(SelectProjectUnitEvent event, Emitter<AddLeadState> emit) {
    emit(state.copyWith(selectedProjectsUnits: event.leadProjectModel, errorMessage: ''));
    final selectedProjects = event.leadProjectModel?.map((e) => e.value?.unitId ?? '').toList();
    _addLeadModel = _addLeadModel.copyWith(unitIds: selectedProjects ?? []);
  }

  FutureOr<void> _onRemoveProjectsUnitEvent(RemoveProjectsUnitEvent event, Emitter<AddLeadState> emit) async {
    final updatedSelectedProjects = state.selectedProjectsUnits?.whereNot((element) => element.title == event.selectedProjects.title).toList();

    emit(state.copyWith(errorMessage: '', selectedProjectsUnits: updatedSelectedProjects));
    _addLeadModel.unitIds?.removeWhere((title) => title == event.selectedProjects.value?.unitId);
  }
}
